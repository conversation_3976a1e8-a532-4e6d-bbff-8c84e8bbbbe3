import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  description: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  time: string;
}

export interface NotificationStats {
  total: number;
  unread: number;
  read: number;
}

/**
 * Hook para gerenciar notificações em tempo real
 * Combina Supabase Realtime + Polling para máxima confiabilidade
 */
export function useNotificationsRealtime() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({ total: 0, unread: 0, read: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  
  // Refs para controle
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const realtimeChannelRef = useRef<any>(null);
  const lastFetchRef = useRef<number>(0);

  /**
   * Calcula estatísticas das notificações
   */
  const calculateStats = useCallback((notificationsList: Notification[]): NotificationStats => {
    const total = notificationsList.length;
    const unread = notificationsList.filter(n => !n.read).length;
    const read = total - unread;
    
    return { total, unread, read };
  }, []);

  /**
   * Busca notificações do banco de dados
   */
  const fetchNotifications = useCallback(async (showLoading = false) => {
    if (!user?.id) return;

    try {
      if (showLoading) setIsLoading(true);

      // Para usuários admin demo, buscar notificações de admin
      let userId = user.id;
      if (user.id === 'admin-demo-id') {
        // Buscar admin real do banco
        const { data: adminUser, error: adminError } = await supabase
          .from('users')
          .select('id')
          .eq('role', 'admin')
          .limit(1)
          .single();

        if (adminError || !adminUser) {
          console.warn('⚠️ Admin demo não encontrou admin real, usando fallback');
          // Usar IDs conhecidos como fallback
          userId = '00000000-0000-0000-0000-000000000001';
        } else {
          userId = adminUser.id;
        }
      }

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('time', { ascending: false })
        .limit(50); // Limitar para performance

      if (error) {
        console.error('❌ Erro ao buscar notificações:', error);
        return;
      }

      const notificationsList = data || [];
      setNotifications(notificationsList);
      setStats(calculateStats(notificationsList));
      lastFetchRef.current = Date.now();

      console.log(`✅ [NotificationsRealtime] ${notificationsList.length} notificações carregadas para userId: ${userId}`);
    } catch (error) {
      console.error('❌ Erro geral ao buscar notificações:', error);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [user?.id, calculateStats]);

  /**
   * Configura Supabase Realtime
   */
  const setupRealtime = useCallback(async () => {
    if (!user?.id) return;

    console.log('🔄 [NotificationsRealtime] Configurando Supabase Realtime...');

    // Limpar canal anterior se existir
    if (realtimeChannelRef.current) {
      supabase.removeChannel(realtimeChannelRef.current);
    }

    // Determinar userId correto
    let userId = user.id;
    if (user.id === 'admin-demo-id') {
      const { data: adminUser } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'admin')
        .limit(1)
        .single();

      userId = adminUser?.id || '00000000-0000-0000-0000-000000000001';
    }

    // Criar novo canal
    const channel = supabase
      .channel('notifications_realtime')
      .on(
        'postgres_changes',
        {
          event: '*', // INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('🔔 [NotificationsRealtime] Mudança detectada:', payload);

          // Atualizar notificações após pequeno delay para garantir consistência
          setTimeout(() => {
            fetchNotifications(false);
          }, 100);
        }
      )
      .subscribe((status) => {
        console.log(`📡 [NotificationsRealtime] Status: ${status}`);
        setIsConnected(status === 'SUBSCRIBED');
      });

    realtimeChannelRef.current = channel;
  }, [user?.id, fetchNotifications]);

  /**
   * Configura polling como backup
   */
  const setupPolling = useCallback(() => {
    // Limpar polling anterior
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Configurar novo polling (a cada 30 segundos)
    pollingIntervalRef.current = setInterval(() => {
      const timeSinceLastFetch = Date.now() - lastFetchRef.current;
      
      // Só fazer polling se não houve fetch recente (evitar duplicação com realtime)
      if (timeSinceLastFetch > 25000) { // 25 segundos
        console.log('🔄 [NotificationsRealtime] Polling backup executado');
        fetchNotifications(false);
      }
    }, 30000); // 30 segundos

    console.log('⏰ [NotificationsRealtime] Polling backup configurado (30s)');
  }, [fetchNotifications]);

  /**
   * Marca notificação como lida
   */
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('❌ Erro ao marcar notificação como lida:', error);
        return false;
      }

      // Atualizar estado local imediatamente
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      );

      console.log('✅ Notificação marcada como lida:', notificationId);
      return true;
    } catch (error) {
      console.error('❌ Erro geral ao marcar notificação como lida:', error);
      return false;
    }
  }, []);

  /**
   * Marca todas as notificações como lidas
   */
  const markAllAsRead = useCallback(async () => {
    if (!user?.id) return false;

    try {
      // Para usuários admin demo, buscar notificações de admin
      let userId = user.id;
      if (user.id === 'admin-demo-id') {
        // Buscar admin real do banco
        const { data: adminUser, error: adminError } = await supabase
          .from('users')
          .select('id')
          .eq('role', 'admin')
          .limit(1)
          .single();

        if (adminError || !adminUser) {
          console.warn('⚠️ Admin demo não encontrou admin real, usando fallback');
          // Usar IDs conhecidos como fallback
          userId = '00000000-0000-0000-0000-000000000001';
        } else {
          userId = adminUser.id;
        }
      }

      console.log('📖 [NotificationsRealtime] Marcando todas como lidas para userId:', userId);

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) {
        console.error('❌ Erro ao marcar todas as notificações como lidas:', error);
        return false;
      }

      // Atualizar estado local
      setNotifications(prev =>
        prev.map(n => ({ ...n, read: true }))
      );

      console.log('✅ Todas as notificações marcadas como lidas');
      return true;
    } catch (error) {
      console.error('❌ Erro geral ao marcar todas as notificações como lidas:', error);
      return false;
    }
  }, [user?.id]);

  /**
   * Remove todas as notificações
   */
  const clearAll = useCallback(async () => {
    if (!user?.id) return false;

    try {
      // Para usuários admin demo, buscar notificações de admin
      let userId = user.id;
      if (user.id === 'admin-demo-id') {
        // Buscar admin real do banco
        const { data: adminUser, error: adminError } = await supabase
          .from('users')
          .select('id')
          .eq('role', 'admin')
          .limit(1)
          .single();

        if (adminError || !adminUser) {
          console.warn('⚠️ Admin demo não encontrou admin real, usando fallback');
          // Usar IDs conhecidos como fallback
          userId = '00000000-0000-0000-0000-000000000001';
        } else {
          userId = adminUser.id;
        }
      }

      console.log('🗑️ [NotificationsRealtime] Removendo todas as notificações para userId:', userId);

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Erro ao limpar notificações:', error);
        return false;
      }

      // Limpar estado local
      setNotifications([]);
      setStats({ total: 0, unread: 0, read: 0 });

      console.log('✅ Todas as notificações removidas com sucesso');
      return true;
    } catch (error) {
      console.error('❌ Erro geral ao limpar notificações:', error);
      return false;
    }
  }, [user?.id]);

  /**
   * Força atualização manual
   */
  const refresh = useCallback(() => {
    console.log('🔄 [NotificationsRealtime] Atualização manual solicitada');
    fetchNotifications(true);
  }, [fetchNotifications]);

  // Efeito principal - Configurar tudo quando o usuário estiver disponível
  useEffect(() => {
    if (!user?.id) {
      setNotifications([]);
      setStats({ total: 0, unread: 0, read: 0 });
      setIsLoading(false);
      return;
    }

    console.log('🚀 [NotificationsRealtime] Inicializando para usuário:', user.id);

    // 1. Buscar notificações iniciais
    fetchNotifications(true);

    // 2. Configurar realtime
    setupRealtime();

    // 3. Configurar polling backup
    setupPolling();

    // Cleanup
    return () => {
      console.log('🧹 [NotificationsRealtime] Limpando recursos...');
      
      if (realtimeChannelRef.current) {
        supabase.removeChannel(realtimeChannelRef.current);
        realtimeChannelRef.current = null;
      }
      
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [user?.id, fetchNotifications, setupRealtime, setupPolling]);

  // Recalcular stats quando notifications mudam
  useEffect(() => {
    setStats(calculateStats(notifications));
  }, [notifications, calculateStats]);

  return {
    // Dados
    notifications,
    stats,
    isLoading,
    isConnected,
    
    // Ações
    markAsRead,
    markAllAsRead,
    clearAll,
    refresh
  };
}
