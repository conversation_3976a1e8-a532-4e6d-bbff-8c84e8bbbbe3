/**
 * Configurações das ações obrigatórias por transição de status
 */

import { StatusTransitionConfig } from '@/types/requiredActions';

export const REQUIRED_ACTIONS_CONFIG: StatusTransitionConfig[] = [
  // FASE 1: Foto do equipamento coletado (REQUISITO IDENTIFICADO)
  {
    fromStatus: 'on_the_way',
    toStatus: 'collected',
    attendanceTypes: ['coleta_conserto'],
    title: 'Equipamento Coletado',
    description: 'Registre a coleta do equipamento com uma foto para documentação',
    allowSkip: true,
    skipReason: 'Permitir pular em casos de adversidade (ex: cliente ausente, equipamento muito grande)',
    requiredActions: [
      {
        type: 'photo',
        label: 'Foto do Equipamento Coletado',
        placeholder: 'Tire uma foto clara do equipamento coletado',
        required: true,
        maxPhotos: 3,
        validation: (photos: File[]) => photos && photos.length > 0
      },
      {
        type: 'text',
        label: 'Estado do Equipamento',
        placeholder: 'Descreva o estado visual do equipamento (riscos, danos, etc.)',
        required: false,
        minLength: 10
      },
      {
        type: 'selection',
        label: 'Condição Geral',
        required: true,
        options: ['Excelente', 'Bom', 'Regular', 'Ruim', 'Muito Danificado'],
        validation: (value: string) => value && value.length > 0
      }
    ]
  },
  
  // Coleta para diagnóstico
  {
    fromStatus: 'on_the_way',
    toStatus: 'collected_for_diagnosis',
    attendanceTypes: ['coleta_diagnostico'],
    title: 'Equipamento Coletado para Diagnóstico',
    description: 'Registre a coleta do equipamento que será diagnosticado',
    allowSkip: true,
    skipReason: 'Permitir pular em casos de adversidade',
    requiredActions: [
      {
        type: 'photo',
        label: 'Foto do Equipamento Coletado',
        placeholder: 'Tire uma foto clara do equipamento coletado',
        required: true,
        maxPhotos: 3,
        validation: (photos: File[]) => photos && photos.length > 0
      },
      {
        type: 'text',
        label: 'Problema Relatado pelo Cliente',
        placeholder: 'Descreva o problema relatado pelo cliente',
        required: true,
        minLength: 10
      },
      {
        type: 'selection',
        label: 'Condição Geral',
        required: true,
        options: ['Excelente', 'Bom', 'Regular', 'Ruim', 'Muito Danificado']
      }
    ]
  },

  // FASE 1: Relatório de serviço em domicílio
  {
    fromStatus: 'in_progress',
    toStatus: 'payment_pending',
    attendanceTypes: ['em_domicilio'],
    title: 'Serviço Concluído',
    description: 'Documente o serviço realizado antes de solicitar o pagamento',
    allowSkip: true,
    skipReason: 'Permitir pular se houver urgência ou problema técnico',
    requiredActions: [
      {
        type: 'text',
        label: 'Relatório do Serviço',
        placeholder: 'Descreva detalhadamente o serviço realizado, peças trocadas, etc.',
        required: true,
        minLength: 20
      },
      {
        type: 'photo',
        label: 'Fotos do Resultado',
        placeholder: 'Tire fotos do equipamento após o reparo (opcional)',
        required: false,
        maxPhotos: 5
      },
      {
        type: 'selection',
        label: 'Status do Reparo',
        required: true,
        options: ['Totalmente Reparado', 'Parcialmente Reparado', 'Necessita Peças', 'Não Reparável']
      }
    ]
  },

  // FASE 1: Confirmação de pagamento
  {
    fromStatus: 'payment_pending',
    toStatus: 'completed',
    attendanceTypes: ['em_domicilio', 'coleta_conserto', 'coleta_diagnostico'],
    title: 'Pagamento Confirmado',
    description: 'Confirme o recebimento do pagamento',
    allowSkip: true,
    skipReason: 'Permitir pular se pagamento será processado posteriormente',
    requiredActions: [
      {
        type: 'selection',
        label: 'Método de Pagamento',
        required: true,
        options: ['Dinheiro', 'Cartão de Débito', 'Cartão de Crédito', 'PIX', 'Transferência', 'A Prazo']
      },
      {
        type: 'text',
        label: 'Valor Recebido',
        placeholder: 'Ex: R$ 150,00',
        required: true,
        minLength: 3,
        validation: (value: string) => {
          // Validação básica de valor monetário
          const cleanValue = value.replace(/[^\d,]/g, '');
          return cleanValue.length > 0;
        }
      },
      {
        type: 'text',
        label: 'Observações do Pagamento',
        placeholder: 'Observações adicionais sobre o pagamento (opcional)',
        required: false
      }
    ]
  }
];

/**
 * Busca a configuração de ação obrigatória para uma transição específica
 */
export const getRequiredActionConfig = (
  fromStatus: string, 
  toStatus: string, 
  attendanceType: string
): StatusTransitionConfig | null => {
  return REQUIRED_ACTIONS_CONFIG.find(config => 
    config.fromStatus === fromStatus && 
    config.toStatus === toStatus && 
    config.attendanceTypes.includes(attendanceType)
  ) || null;
};

/**
 * Verifica se uma transição requer ações obrigatórias
 */
export const requiresActions = (
  fromStatus: string, 
  toStatus: string, 
  attendanceType: string
): boolean => {
  return getRequiredActionConfig(fromStatus, toStatus, attendanceType) !== null;
};
