
import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserRole } from '@/types';
import { toast } from 'sonner';

const loginSchema = z.object({
  email: z.string().refine(val => {
    if (val.toLowerCase() === 'admin' || val.toLowerCase() === 'oficina') return true;
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val);
  }, { message: 'E-mail inválido' }),
  password: z.string().min(1, 'Senha é obrigatória'),
});

export type LoginValues = z.infer<typeof loginSchema>;

interface LoginFormProps {
  activeRole: UserRole;
  onToggleRegisterForm: () => void;
}

export type LoginFormRef = {
  setValue: (name: 'email' | 'password', value: string) => void;
};

export const LoginForm = forwardRef<LoginFormRef, LoginFormProps>((
  { activeRole, onToggleRegisterForm }, 
  ref
) => {
  const navigate = useNavigate();
  const { signIn } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  const form = useForm<LoginValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Expose setValue method to parent component
  useImperativeHandle(ref, () => ({
    setValue: (name: 'email' | 'password', value: string) => {
      form.setValue(name, value);
    }
  }));

  const onSubmit = async (values: LoginValues) => {
    setIsSubmitting(true);
    setLoginError(null);
    
    try {
      console.log(`Tentando login com: ${values.email} / ${values.password}`);
      await signIn({email: values.email, password: values.password});
    } catch (error: any) {
      console.error("Falha no login:", error);
      setLoginError(error?.message || "Email ou senha inválidos");
      toast.error("Falha no login. Email ou senha inválidos");
      form.setValue('password', '');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {loginError && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{loginError}</AlertDescription>
        </Alert>
      )}
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>E-mail</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Senha</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="••••••" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full" disabled={isSubmitting}>
            {isSubmitting ? "Entrando..." : "Entrar"}
          </Button>
          
          <p className="text-sm text-center">
            Não tem uma conta?{" "}
            <Button variant="link" className="p-0 h-auto" onClick={onToggleRegisterForm}>
              Registre-se
            </Button>
          </p>
        </form>
      </Form>
    </>
  );
});

LoginForm.displayName = 'LoginForm';
