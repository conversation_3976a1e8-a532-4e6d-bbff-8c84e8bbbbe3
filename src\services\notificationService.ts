
// Fix the function on line 70 to use userId instead of user_id
import { supabase } from '@/integrations/supabase/client';
import { Notification } from '@/types';

export const createNotification = async (notification: Omit<Notification, 'id' | 'time' | 'read'>) => {
  try {
    // Ensure userId is used instead of user_id
    const { data, error } = await supabase
      .from('notifications')
      .insert({
        title: notification.title,
        description: notification.description,
        type: notification.type || 'info',
        userId: notification.userId, // Changed from user_id to userId
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      return null;
    }

    return data;
  } catch (err) {
    console.error('Error in createNotification:', err);
    return null;
  }
};

// Add exports for other notification service functions
export const getAll = async (userId: string): Promise<Notification[]> => {
  try {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('time', { ascending: false })
      .limit(10);
      
    if (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
    
    return data || [];
  } catch (err) {
    console.error('Error in getNotifications:', err);
    return [];
  }
};

export const markAsRead = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', id);
      
    return !error;
  } catch (err) {
    console.error('Error marking notification as read:', err);
    return false;
  }
};

export const markAllAsRead = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('user_id', userId)
      .eq('read', false);
      
    return !error;
  } catch (err) {
    console.error('Error marking all notifications as read:', err);
    return false;
  }
};

export const subscribeToNotifications = (userId: string, callback: (notification: Notification) => void) => {
  return supabase
    .channel('notification-changes')
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'notifications',
      filter: `user_id=eq.${userId}`,
    }, payload => {
      const newNotification = payload.new as Notification;
      callback(newNotification);
    })
    .subscribe();
};

// Group all exports into a single notificationService object for consistency
export const notificationService = {
  createNotification,
  getAll,
  markAsRead,
  markAllAsRead,
  subscribeToNotifications
};
