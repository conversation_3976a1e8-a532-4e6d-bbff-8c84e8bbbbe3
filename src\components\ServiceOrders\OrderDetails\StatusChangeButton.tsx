import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ServiceOrder } from '@/types';
import { getServiceFlow } from '@/utils/serviceFlowUtils';
import { useAppData } from '@/hooks/useAppData';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';

interface StatusChangeButtonProps {
  order: ServiceOrder;
  onOrderUpdated: (updatedOrder: ServiceOrder) => void;
}

const StatusChangeButton: React.FC<StatusChangeButtonProps> = ({ order, onOrderUpdated }) => {
  const { updateServiceOrder } = useAppData();
  const [isUpdating, setIsUpdating] = useState(false);

  // Validate and ensure proper attendance type
  const attendanceType = order.serviceAttendanceType || "em_domicilio";
  const validType = ["em_domicilio", "coleta_conserto", "coleta_diagnostico"].includes(attendanceType)
    ? attendanceType as "em_domicilio" | "coleta_conserto" | "coleta_diagnostico"
    : "em_domicilio";

  // Get the service flow based on the validated attendance type
  const serviceFlow = getServiceFlow(validType);
  
  // Filter out the current status from the available options
  const availableStatuses = serviceFlow.filter(step => step.status !== order.status);

  const handleUpdateStatus = async (newStatus: string) => {
    if (isUpdating) return;

    setIsUpdating(true);
    try {
      const updatedOrder = {
        ...order,
        status: newStatus
      };
      
      const success = await updateServiceOrder(order.id, { status: newStatus });

      if (success) {
        const nextStep = serviceFlow.find(step => step.status === newStatus);
        toast.success(`Status atualizado para: ${nextStep?.label || newStatus}`);
        onOrderUpdated(updatedOrder);
      } else {
        toast.error('Não foi possível atualizar o status.');
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status do serviço.');
    } finally {
      setIsUpdating(false);
    }
  };

  if (availableStatuses.length === 0) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isUpdating}>
          {isUpdating ? 'Atualizando...' : 'Alterar Status'} <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {availableStatuses.map((status) => (
          <DropdownMenuItem 
            key={status.key}
            onClick={() => handleUpdateStatus(status.status)}
          >
            <div className="flex items-center">
              <span className="mr-2">{status.icon}</span>
              <span>{status.label}</span>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusChangeButton;
