
import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ServiceOrderStatus } from '@/types';
import { useServiceFlow } from '@/hooks/useServiceFlow';
import StatusDropdownItems from './components/StatusDropdownItems';
import StatusDropdownTrigger from './StatusComponents/StatusDropdownTrigger';
import { toast } from 'sonner';

interface StatusDropdownProps {
  status: ServiceOrderStatus;
  orderId: string;
  onUpdateStatus?: (id: string, status: ServiceOrderStatus) => Promise<void>;
  serviceAttendanceType: 'em_domicilio' | 'coleta_conserto' | 'coleta_diagnostico';
}

const StatusDropdown: React.FC<StatusDropdownProps> = ({
  status,
  orderId,
  onUpdateStatus,
  serviceAttendanceType,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { serviceFlow } = useServiceFlow(serviceAttendanceType, status);

  const handleStatusClick = (newStatus: ServiceOrderStatus) => async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!onUpdateStatus || isUpdating || status === newStatus) {
      console.log(`StatusDropdown: Cannot update status. onUpdateStatus exists: ${!!onUpdateStatus}, isUpdating: ${isUpdating}, same status: ${status === newStatus}`);
      return;
    }
    
    try {
      setIsUpdating(true);
      
      // Verify the status exists in the flow
      const statusExists = serviceFlow.some(step => step.status === newStatus);
      if (!statusExists) {
        console.error(`StatusDropdown: Invalid status ${newStatus} for service type ${serviceAttendanceType}`);
        toast.error(`Status inválido: ${newStatus}`);
        setIsUpdating(false);
        return;
      }
      
      // Close dropdown first to improve UI responsiveness
      setIsOpen(false);
      
      console.log(`StatusDropdown: Status update initiated: ${status} -> ${newStatus} for order ${orderId}`);
      
      // Then update the status
      await onUpdateStatus(orderId, newStatus);
      
      console.log(`StatusDropdown: Status updated to ${newStatus} for order ${orderId}`);
      toast.success(`Status atualizado para ${newStatus}`);
    } catch (error) {
      console.error(`StatusDropdown: Error updating status to ${newStatus}:`, error);
      toast.error("Erro ao atualizar status");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger disabled={isUpdating} asChild>
        <div className="cursor-pointer">
          <StatusDropdownTrigger 
            status={status} 
            isUpdatingStatus={isUpdating} 
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56 z-50">
        <StatusDropdownItems
          serviceFlow={serviceFlow}
          currentStatus={status}
          onStatusClick={handleStatusClick}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusDropdown;
