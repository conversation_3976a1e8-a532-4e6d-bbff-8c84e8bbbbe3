
import React from 'react';
import { ServiceOrderStatus } from '@/types';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { ServiceFlowStep } from '@/utils/serviceFlowUtils';
import { Check } from 'lucide-react';

interface StatusDropdownItemsProps {
  serviceFlow: ServiceFlowStep[];
  currentStatus: ServiceOrderStatus;
  onStatusClick: (status: ServiceOrderStatus) => (e: React.MouseEvent) => void;
}

const StatusDropdownItems: React.FC<StatusDropdownItemsProps> = ({ 
  serviceFlow, 
  currentStatus, 
  onStatusClick 
}) => {
  return (
    <>
      {serviceFlow.map((step) => (
        <DropdownMenuItem 
          key={step.status}
          onClick={onStatusClick(step.status as ServiceOrderStatus)}
          disabled={currentStatus === step.status}
          className={`flex items-center justify-between gap-2 px-3 py-2 text-sm cursor-pointer
            ${currentStatus === step.status ? 'bg-muted' : 'hover:bg-primary/5'}`}
          data-status={step.status}
        >
          <div className="flex items-center gap-2">
            {step.icon}
            <span>{step.label}</span>
          </div>
          {currentStatus === step.status && <Check className="h-4 w-4" />}
        </DropdownMenuItem>
      ))}
    </>
  );
};

export default StatusDropdownItems;
