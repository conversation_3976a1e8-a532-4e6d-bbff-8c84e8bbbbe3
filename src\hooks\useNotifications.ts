
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { notificationService } from '@/services/notificationService';
import { Notification } from '@/types';
import { supabase } from '@/integrations/supabase/client';

export function useNotifications() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  
  const fetchNotifications = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const data = await notificationService.getAll(user.id);
      setNotifications(data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (id: string) => {
    const success = await notificationService.markAsRead(id);
    if (success) {
      setNotifications(prevNotifications => 
        prevNotifications.map(notif => 
          notif.id === id ? { ...notif, read: true } : notif
        )
      );
    }
    return success;
  };

  const markAllAsRead = async () => {
    if (!user?.id) return false;
    
    const success = await notificationService.markAllAsRead(user.id);
    if (success) {
      setNotifications(prevNotifications => 
        prevNotifications.map(notif => ({ ...notif, read: true }))
      );
    }
    return success;
  };

  // Carrega notificações iniciais e configura inscrição para notificações em tempo real
  useEffect(() => {
    if (!user?.id) return;

    fetchNotifications();

    // Inscreva-se para atualizações em tempo real
    const channel = notificationService.subscribeToNotifications(user.id, (newNotification) => {
      setNotifications(prev => [newNotification, ...prev]);
    });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    loading,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refresh: fetchNotifications
  };
}
