
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types';
import { Button } from '@/components/ui/button';
import { RegisterForm } from '@/components/auth/RegisterForm';
import { LoginForm, LoginFormRef } from '@/components/auth/LoginForm';
import { RoleSelector } from '@/components/auth/RoleSelector';
import { LoginPageLayout } from '@/components/auth/LoginPageLayout';
import { AuthLoading } from '@/components/auth/AuthLoading';
import AccountsDebugPanel from '@/components/admin/AccountsDebugPanel';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const [activeRole, setActiveRole] = useState<UserRole>('admin');
  const [showRegister, setShowRegister] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // Form reference for setting demo credentials
  const loginFormRef = useRef<LoginFormRef>(null);

  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, isLoading, navigate]);

  const toggleRegisterForm = () => {
    setShowRegister(!showRegister);
  };
  
  const toggleDebugPanel = () => {
    setShowDebugPanel(!showDebugPanel);
  };

  const setDemoCredentials = (role: UserRole) => {
    setActiveRole(role);
    
    let email = '';
    let password = '';
    
    switch (role) {
      case 'admin':
        email = 'admin';
        password = 'admin';
        break;
      case 'technician':
        email = '<EMAIL>';
        password = '1234';
        break;
      case 'client':
        email = '<EMAIL>';
        password = '1234';
        break;
      case 'workshop':
        email = '<EMAIL>';
        password = '1234';
        break;
    }
    
    // Update form values using the ref
    if (loginFormRef.current) {
      loginFormRef.current.setValue('email', email);
      loginFormRef.current.setValue('password', password);
    }
  };

  if (isLoading) {
    return <AuthLoading />;
  }

  const footerContent = (
    <>
      <p className="text-sm text-center text-muted-foreground">
        {showRegister 
          ? "O registro cria um usuário com perfil de cliente. Entre em contato com o administrador para obter outros perfis."
          : "Para uma experiência completa, você também pode usar as credenciais de demonstração acima."}
      </p>
      <div className="w-full flex justify-center pt-2">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={toggleDebugPanel} 
          className="text-xs text-muted-foreground"
        >
          {showDebugPanel ? "Ocultar Debug" : "Debug de Contas"}
        </Button>
      </div>
    </>
  );

  return (
    <LoginPageLayout
      title={showRegister ? "Registro" : "Login"}
      description={showRegister 
        ? "Crie sua conta para acessar o sistema" 
        : "Acesse o sistema com suas credenciais"}
      footer={footerContent}
      belowCard={showDebugPanel && (
        <div className="mt-6 w-full max-w-3xl">
          <AccountsDebugPanel />
        </div>
      )}
    >
      {showRegister ? (
        <RegisterForm onToggleForm={toggleRegisterForm} />
      ) : (
        <>
          <RoleSelector 
            activeRole={activeRole}
            onRoleChange={setDemoCredentials}
          />
          <LoginForm 
            ref={loginFormRef}
            activeRole={activeRole} 
            onToggleRegisterForm={toggleRegisterForm}
          />
        </>
      )}
    </LoginPageLayout>
  );
};

export default Login;
