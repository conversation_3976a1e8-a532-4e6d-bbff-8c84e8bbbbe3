
import React, { ReactNode } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';

interface LoginPageLayoutProps {
  title: string;
  description: string;
  children: ReactNode;
  footer?: ReactNode;
  belowCard?: ReactNode;
}

export const LoginPageLayout: React.FC<LoginPageLayoutProps> = ({ 
  title, 
  description, 
  children, 
  footer,
  belowCard 
}) => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
      <div className="w-full max-w-md animate-fade-in">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">EletroFix Hub Pro</h1>
          <p className="text-gray-600">Sistema de Gestão para Manutenção de Eletrodomésticos</p>
        </div>
        
        <Card className="w-full">
          <CardHeader>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </CardHeader>
          <CardContent>{children}</CardContent>
          {footer && (
            <CardFooter className="flex-col space-y-2">
              {footer}
            </CardFooter>
          )}
        </Card>
        
        {belowCard}
      </div>
    </div>
  );
};
