
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { notificationService } from '@/services/notificationService';
import { Notification } from '@/types';
import { supabase } from '@/integrations/supabase/client';

export function useNotifications() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  
  const fetchNotifications = async () => {
    if (!user?.id) {
      console.log('🔔 [useNotifications] Usuário não encontrado:', user);
      return;
    }

    try {
      setLoading(true);
      console.log('🔔 [useNotifications] Buscando notificações para usuário:', user.id);
      const data = await notificationService.getAll(user.id);
      console.log('🔔 [useNotifications] Notificações encontradas:', data);
      setNotifications(data);
    } catch (error) {
      console.error('❌ [useNotifications] Erro ao buscar notificações:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (id: string) => {
    const success = await notificationService.markAsRead(id);
    if (success) {
      setNotifications(prevNotifications => 
        prevNotifications.map(notif => 
          notif.id === id ? { ...notif, read: true } : notif
        )
      );
    }
    return success;
  };

  const markAllAsRead = async () => {
    if (!user?.id) return false;
    
    const success = await notificationService.markAllAsRead(user.id);
    if (success) {
      setNotifications(prevNotifications => 
        prevNotifications.map(notif => ({ ...notif, read: true }))
      );
    }
    return success;
  };

  // Carrega notificações iniciais e configura inscrição para notificações em tempo real
  useEffect(() => {
    console.log('🔔 [useNotifications] useEffect executado. User:', user);
    console.log('🔔 [useNotifications] User ID:', user?.id);

    if (!user?.id) {
      console.log('🔔 [useNotifications] Usuário não tem ID, saindo do useEffect');
      return;
    }

    console.log('🔔 [useNotifications] Chamando fetchNotifications...');
    fetchNotifications();

    // Inscreva-se para atualizações em tempo real
    console.log('🔔 [useNotifications] Configurando subscription para user ID:', user.id);
    const channel = notificationService.subscribeToNotifications(user.id, (newNotification) => {
      console.log('🔔 [useNotifications] Nova notificação recebida via subscription:', newNotification);
      setNotifications(prev => [newNotification, ...prev]);
    });

    return () => {
      console.log('🔔 [useNotifications] Limpando subscription');
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    loading,
    unreadCount,
    markAsRead,
    markAllAsRead,
    refresh: fetchNotifications
  };
}
