
import React from 'react';
import { ServiceOrderStatus } from '@/types';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { ServiceFlowStep } from '@/utils/serviceFlowUtils';
import { Check } from 'lucide-react';

interface StatusDropdownItemsProps {
  serviceFlow: ServiceFlowStep[];
  currentStatus: ServiceOrderStatus;
  onStatusClick: (status: ServiceOrderStatus) => (e: React.MouseEvent) => void;
}

const StatusDropdownItems: React.FC<StatusDropdownItemsProps> = ({
  serviceFlow,
  currentStatus,
  onStatusClick
}) => {
  console.log(`🔍 [StatusDropdownItems] RENDERIZANDO - serviceFlow:`, serviceFlow.map(s => s.status));
  console.log(`🔍 [StatusDropdownItems] currentStatus:`, currentStatus);
  console.log(`🔍 [StatusDropdownItems] onStatusClick disponível:`, !!onStatusClick);

  return (
    <>
      {serviceFlow.map((step) => {
        const handleClick = (e: React.MouseEvent) => {
          console.log(`🚨 [StatusDropdownItems] CLIQUE INTERCEPTADO! Status: ${step.status}`);
          console.log(`🚨 [StatusDropdownItems] Evento:`, { type: e.type, target: e.target });

          // Prevenir propagação
          e.preventDefault();
          e.stopPropagation();

          // Chamar a função original
          const originalHandler = onStatusClick(step.status as ServiceOrderStatus);
          if (originalHandler) {
            console.log(`🔄 [StatusDropdownItems] Chamando handler original...`);
            originalHandler(e);
          } else {
            console.error(`❌ [StatusDropdownItems] Handler original não retornado!`);
          }
        };

        return (
          <div
            key={step.status}
            onClick={handleClick}
            className={`flex items-center justify-between gap-2 px-3 py-2 text-sm cursor-pointer
              ${currentStatus === step.status ? 'bg-muted' : 'hover:bg-primary/5'}
              ${currentStatus === step.status ? 'opacity-50 cursor-not-allowed' : ''}`}
            data-status={step.status}
            style={{
              pointerEvents: currentStatus === step.status ? 'none' : 'auto',
              userSelect: 'none'
            }}
          >
            <div className="flex items-center gap-2">
              {step.icon}
              <span>{step.label}</span>
            </div>
            {currentStatus === step.status && <Check className="h-4 w-4" />}
          </div>
        );
      })}
    </>
  );
};

export default StatusDropdownItems;
