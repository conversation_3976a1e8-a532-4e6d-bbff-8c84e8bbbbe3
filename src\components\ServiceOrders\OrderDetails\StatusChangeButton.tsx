import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ServiceOrder } from '@/types';
import { getServiceFlow } from '@/utils/serviceFlowUtils';
import { useAppData } from '@/hooks/useAppData';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';

interface StatusChangeButtonProps {
  order: ServiceOrder;
  onOrderUpdated: (updatedOrder: ServiceOrder) => void;
}

const StatusChangeButton: React.FC<StatusChangeButtonProps> = ({ order, onOrderUpdated }) => {
  const { updateServiceOrder } = useAppData();
  const [isUpdating, setIsUpdating] = useState(false);

  // Validate and ensure proper attendance type
  const attendanceType = order.serviceAttendanceType || "em_domicilio";
  const validType = ["em_domicilio", "coleta_conserto", "coleta_diagnostico"].includes(attendanceType)
    ? attendanceType as "em_domicilio" | "coleta_conserto" | "coleta_diagnostico"
    : "em_domicilio";

  // Get the service flow based on the validated attendance type
  const serviceFlow = getServiceFlow(validType);

  // Show all statuses (like in the main listing), not just filtered ones
  const availableStatuses = serviceFlow;

  const handleUpdateStatus = async (newStatus: string) => {
    if (isUpdating || newStatus === order.status) return;

    console.log(`🔄 [StatusChangeButton] Atualizando status: ${order.status} → ${newStatus} para ordem ${order.id}`);

    setIsUpdating(true);
    try {
      const success = await updateServiceOrder(order.id, { status: newStatus });

      if (success) {
        const updatedOrder = {
          ...order,
          status: newStatus
        };

        const nextStep = serviceFlow.find(step => step.status === newStatus);
        toast.success(`Status atualizado para: ${nextStep?.label || newStatus}`);

        console.log(`✅ [StatusChangeButton] Status atualizado com sucesso, chamando onOrderUpdated`);
        onOrderUpdated(updatedOrder);
      } else {
        console.error(`❌ [StatusChangeButton] Falha ao atualizar status no backend`);
        toast.error('Não foi possível atualizar o status.');
      }
    } catch (error) {
      console.error('❌ [StatusChangeButton] Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status do serviço.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Always show the dropdown, even if current status is selected (it will be disabled)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isUpdating}>
          {isUpdating ? 'Atualizando...' : 'Alterar Status'} <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {availableStatuses.map((status) => (
          <DropdownMenuItem
            key={status.key}
            onClick={() => handleUpdateStatus(status.status)}
            disabled={order.status === status.status}
            className={`flex items-center justify-between gap-2 px-3 py-2 text-sm cursor-pointer
              ${order.status === status.status ? 'bg-muted' : 'hover:bg-primary/5'}`}
          >
            <div className="flex items-center gap-2">
              <span>{status.icon}</span>
              <span>{status.label}</span>
            </div>
            {order.status === status.status && (
              <span className="text-xs text-muted-foreground">Atual</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusChangeButton;
